import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:in_app_review/in_app_review.dart';

import '../presentation/pages/component.dart';
import '../presentation/pages/mobile/constants.dart';
import '../states/app_state.dart';

class StreakReviewDialog extends Dialog {
  StreakReviewDialog({
    super.key,
    required this.onDismiss,
  });

  final VoidCallback onDismiss;

  Future<void> _requestReview() async {
    try {
      final InAppReview inAppReview = InAppReview.instance;
      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
      }
    } catch (e) {
      print('Error requesting review: $e');
    }
  }

  Future<void> _markReviewShown() async {
    await LDAppState().setStreakReviewShown();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      contentPadding: EdgeInsets.fromLTRB(24, 32, 24, 16),
      actionsPadding: EdgeInsets.fromLTRB(24, 0, 24, 24),
      actionsAlignment: MainAxisAlignment.center,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Celebration icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: LDColors.mainLime.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.celebration,
              size: 40,
              color: LDColors.mainLime,
            ),
          ),
          SizedBox(height: 24),
          // Title
          Text(
            FlutterI18n.translate(context, 'review.streak.title'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 16),
          // Description
          Text(
            FlutterI18n.translate(context, 'review.streak.description'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
          SizedBox(height: 8),
          // Review request
          Text(
            FlutterI18n.translate(context, 'review.streak.request'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              height: 1.4,
            ),
          ),
        ],
      ),
      actions: [
        Column(
          children: [
            // Rate now button
            SizedBox(
              width: double.infinity,
              child: LDButton(
                label: FlutterI18n.translate(context, 'review.streak.rate_now'),
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _markReviewShown();
                  _requestReview();
                  onDismiss();
                },
                backgroundColor: LDColors.mainLime,
              ),
            ),
            SizedBox(height: 12),
            // Maybe later button
            SizedBox(
              width: double.infinity,
              child: LDButton(
                label:
                    FlutterI18n.translate(context, 'review.streak.maybe_later'),
                backgroundColor: Colors.transparent,
                textColor: LDColors.mainGrey,
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _markReviewShown();
                  onDismiss();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
